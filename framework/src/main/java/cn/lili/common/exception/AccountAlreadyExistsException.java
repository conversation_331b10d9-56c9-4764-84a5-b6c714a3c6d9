package cn.lili.common.exception;

import cn.lili.common.enums.ResultCode;

/**
 * 账号已存在异常
 * 用于处理账号打通时账号已被注册的情况
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public class AccountAlreadyExistsException extends ServiceException {

    public AccountAlreadyExistsException() {
        super(ResultCode.USER_EXIST);
    }

    public AccountAlreadyExistsException(String message) {
        super(ResultCode.USER_EXIST, message);
    }

    public AccountAlreadyExistsException(ResultCode resultCode) {
        super(resultCode);
    }

    public AccountAlreadyExistsException(ResultCode resultCode, String message) {
        super(resultCode, message);
    }
}
