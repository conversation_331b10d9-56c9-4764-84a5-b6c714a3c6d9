package cn.lili.modules.jrzh_other.bestsign.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.connect.entity.dto.SSQAccountAudit;
import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_bases.Func;
import cn.lili.modules.jrzh_other.bestsign.config.BestSignConfig;
import cn.lili.modules.jrzh_other.bestsign.connector.BestSignConnector;
import cn.lili.modules.jrzh_other.bestsign.constant.BestSignPathEnum;
import cn.lili.modules.jrzh_other.bestsign.dto.*;
import cn.lili.modules.jrzh_other.bestsign.service.IBestSignService;
import cn.lili.modules.jrzh_other.core.utils.OtherApiUtils;
import cn.lili.modules.jrzh_other.core_api.constant.OtherApiTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023/1/29 16:25
 * @Description: 上上签服务
 * @Version: 1.0
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BestSignServiceImpl implements IBestSignService {
//    private final BladeRedis bladeRedis;
    private final BestSignConnector connector;
    private static final Integer IZ_FRONT = 1;
    private static final String PARSE_TIME_FORMAT = "yyyy-MM-dd";
    private static final String SIGN_KEY = "bestSignLinkKey:";
    private final BestSignConfig bestSignConfig;

    /**
     * 签署时合同不可操作错误码
     */
    private final static Integer CONTRACT_SIGNED = 241423;
    /**
     * 签署时该用户已签署错误码
     */
    private final static Integer SIGNER_SIGNED = 241424;
//
//    @Override
//    public Boolean entBankVerify(SSQAccountAudit accountAudit) {
//        //不足6位补0
//        String areaCode = accountAudit.getBankAreaCode();
//        accountAudit.setBankAreaCode((areaCode + "*********").substring(0, 6));
//        JSONObject jsonQuery = JSONUtil.parseObj(accountAudit);
//        //设置方法名称、方法厂商、方法请求地址
//        String supplier = ApiSupplier.BEST_SIGN_BANK_SERVICE.getCode();
//        BestSignPathEnum method = BestSignPathEnum.ENT_BANK_VERIFY;
//        String name = method.toString();
//        TreeMap<String, Object> queryTree = new TreeMap<>(jsonQuery);
//        //调用接口并缓存
//        OtherApiUtils.doActionWithRecord(queryTree, supplier, name, SSQResult.class, Duration.ofDays(30), () -> {
//            String req = JSONUtil.toJsonStr(accountAudit);
//            SSQResult ssqResult = connector.post(method.getPath(), req);
//            Integer success = JSONUtil.parseObj(ssqResult.getData()).getInt("result");
//            if (!(success.equals(3) || success.equals(1))) {
//                throw new ServiceException("对公认证失败");
//            }
//            return ssqResult;
//        });
//        return true;
//    }
//
//    @Override
//    public Boolean personBankVerify(String bankcard, String personName) {
//        TreeMap<String, Object> queryTree = new TreeMap<>();
//        queryTree.put("bankcard", bankcard);
//        queryTree.put("name", personName);
//        //设置方法名称、方法厂商、方法请求地址
//        String supplier = ApiSupplier.BEST_SIGN_BANK_SERVICE.getCode();
//        BestSignPathEnum method = BestSignPathEnum.PERSON_BANK_VERIFY;
//        String name = method.toString();
//        //调用接口并缓存
//        SSQResult result = OtherApiUtils.doActionWithRecord(queryTree, supplier, name, SSQResult.class, Duration.ofDays(30), () -> {
//            SSQResult ssqResult = connector.post(method.getPath(), JSONUtil.toJsonStr(queryTree));
//            return ssqResult;
//        });
//        Integer success = JSONUtil.parseObj(result.getData()).getInt("result");
//        return success.equals(1);
//    }
//
    @Override
    public boolean uploadSignImg(String account, String imageData, String imageName) {
        Map<String, Object> req = new HashMap<>();
        req.put("account", account);
        req.put("imageData", imageData);
        req.put("imageName", imageName);
        connector.post(BestSignPathEnum.UPLOAD.getPath(), JSONUtil.toJsonStr(req), false);
        return true;
    }

    @Override
    public boolean generateSignImg(String companyId, Integer customerType) {
        Map<String, Object> req = new HashMap<>();
        final Integer izPersonal = 1;
        req.put("account", companyId);
        BestSignPathEnum signImgEnum = Objects.equals(izPersonal, customerType)
                ? BestSignPathEnum.CREATE : BestSignPathEnum.CREATE_ENT;
        connector.post(signImgEnum.getPath(), JSONUtil.toJsonStr(req), true);
        return true;
    }

    @Override
    public BladeFile downloadSignImg(String imageName, String companyId) {
        Map<String, Object> req = new HashMap<>();
        req.put("account", companyId);
        final String defaultSuffix = ".png";
        String imgName = StringUtil.isNotBlank(imageName) ? imageName : "default" + defaultSuffix;
        return connector.getToFile(BestSignPathEnum.DOWNLOAD.getPath(), req,
                imgName);
    }
//
//    @Override
//    public boolean verifySignature(String contractId, String fhash) {
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", contractId);
//        req.putOnce("fhash", fhash);
//        SSQResult ssqResult = connector.post(BestSignPathEnum.ONLINE.getPath(), JSONUtil.toJsonStr(req), true);
//        return JSONUtil.parseObj(ssqResult.getData()).getInt("result").equals(1);
//    }
//
//    @Override
//    public ContractReturnData templateContractCreate(SsqContractGenParam ssqContractGenParam) {
//        Map<String, Object> templateValues = ssqContractGenParam.getTemplateValues();
//        Map<String, Object> groupValues = ssqContractGenParam.getGroupValues();
//        //合同Token 请求参数
//        String useTemplateReq = genTokenReq(ssqContractGenParam.getTemplateId(), groupValues, templateValues);
//        //生成合同Token 再生成合同
//        String contractId = ssqCreateContract(ssqContractGenParam, useTemplateReq);
//        //合同下载
//        String link = downLoadContractById(contractId, IdWorker.getIdStr()).getLink();
//        ContractReturnData contractReturnData = new ContractReturnData(link, contractId);
//        contractReturnData.setContractVar(useTemplateReq);
//        return contractReturnData;
//    }
//
//    @Override
//    public void contractAutoSign(String contractId, String templateId, JSONObject varsJSON) {
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", contractId);
//        req.putOnce("tid", templateId);
//        req.putOnce("vars", varsJSON);
//        SSQResult post = connector.post(BestSignPathEnum.CONTRACT_SIGN_TEMPLATE.getPath(), JSONUtil.toJsonStr(req), false);
//        if (SIGNER_SIGNED.equals(post.getErrno()) || CONTRACT_SIGNED.equals(post.getErrno()) || post.getErrno() == 0) {
//            return;
//        } else {
//            throw new ServiceException("签署失败,合同已过期或处于不可操作阶段");
//        }
//    }
//
//    @Override
//    public String contractSign(String contractId, String templateId, String account, Integer genType) {
////        final Integer TEMPLATE = 1;
////        final Integer CUSTOMIZE_TEMPLATE = 2;
////        String key = SIGN_KEY + contractId + account;
////        if (bladeRedis.exists(key)) {
////            return bladeRedis.get(key);
////        }
////        //根据合同生成方式进行签署
////        String longUrl = null;
////        if (TEMPLATE.equals(genType)) {
////            longUrl = contractSend(account);
////        } else {
////            longUrl = templateSkipSign(null);
////        }
////
////        bladeRedis.setEx(key, longUrl, Duration.ofDays(1));
////        return longUrl;
//        return null;
//    }
//
//    @Override
//    public JSONArray getTemplateFields(String templateId) {
//        JSONObject req = new JSONObject();
//        req.putOnce("tid", templateId);
//        req.putOnce("isRetrieveAllVars", "1");
//        SSQResult ssqResult = connector.post(BestSignPathEnum.TEMPLATE_GET_TEMPLATE_VARS.getPath(), JSONUtil.toJsonStr(req));
//        JSONObject resultDataJSON = JSONUtil.parseObj(ssqResult.getData());
//        List<SsqTemplateVars> templateVars = JSONUtil.toList(resultDataJSON.getJSONArray("templateVars"), SsqTemplateVars.class);
//        return filterFieldsByGroup(templateVars);
//    }
//
//    @Override
//    public String skipTemplatePage() {
//        BestSignConfig bestSignConfig = getBestSignConfig();
//        JSONObject req = new JSONObject();
//        req.putOnce("account", bestSignConfig.getAccount());
//        SSQResult ssqResult = connector.post(BestSignPathEnum.PAGE_TEMPLATE_CREATE.getPath(), JSONUtil.toJsonStr(req));
//        return JSONUtil.parseObj(ssqResult.getData()).getStr("url");
//    }
//
//    @Override
//    public String preview(String templateId) {
//        BestSignConfig bestSignConfig = getBestSignConfig();
//        String account = bestSignConfig.getAccount();
//        JSONObject req = new JSONObject();
//        req.putOnce("tid", templateId);
//        req.putOnce("isShowVars", "1");
//        req.putOnce("account", account);
//        SSQResult ssqResult = connector.post(BestSignPathEnum.PAGE_TEMPLATE_PREVIEW.getPath(), JSONUtil.toJsonStr(req));
//        return JSONUtil.parseObj(ssqResult.getData()).getStr("url");
//    }
//
//    @Override
//    public List<SsqTemplate> listContractTemplate(Query query) {
//        JSONObject req = new JSONObject();
//        req.putOnce("pageSize", "100");
//        req.putOnce("pageNum", "1");
//        SSQResult ssqResult = connector.post(BestSignPathEnum.LIST_TEMPLATES.getPath(), JSONUtil.toJsonStr(req));
//        JSONArray templates = JSONUtil.parseObj(ssqResult.getData()).getJSONArray("templates");
//        return JSONUtil.toList(templates, SsqTemplate.class);
//    }
//
    @Override
    public JSONObject getContractStatus(String contractId) {
        //获取合同状态
        JSONObject contractReq = new JSONObject();
        contractReq.putOnce("contractId", contractId);
        //获取签署人状态 k:签署人id v：签署状态
        SSQResult signerStatusResult = connector.post(BestSignPathEnum.CONTRACT_GET_SIGNER_STATUS.getPath(), JSONUtil.toJsonStr(contractReq));
        JSONObject ssqContractSigner = JSONUtil.parseObj(signerStatusResult.getData());
        return ssqContractSigner;
    }

    @Override
    public BladeFile downLoadContract(String contractId, String contractTitle) {
        JSONObject contractReq = new JSONObject();
        contractReq.putOnce("contractId", contractId);
//        return connector.getToFile(BestSignPathEnum.CONTRACT_DOWNLOAD.getPath(), contractReq, contractTitle + IdWorker.getIdStr() + ".pdf");
        return connector.getToFile(BestSignPathEnum.CONTRACT_DOWNLOAD.getPath(), contractReq, IdWorker.getIdStr() + ".pdf");
    }

    @Override
    public void contractRevoke(String contractId) {
        JSONObject contractReq = new JSONObject();
        contractReq.putOnce("contractId", contractId);
        SSQResult post = connector.post(BestSignPathEnum.CONTRACT_CANCEL.getPath(), JSONUtil.toJsonStr(contractReq), false);
        if (!post.getErrno().equals(0) && !"contract has been expired can not operated".equals(post.getErrmsg())) {
            throw new ServiceException("合同撤销失败");
        }
    }

    @Override
    public void contractRevoke(List<String> contractIds) {
        if (CollectionUtil.isNotEmpty(contractIds)) {
            for (String contractId : contractIds) {
                contractRevoke(contractId);
            }
        }
    }
//
//    @Override
//    public String previewContract(String contractId, String accountId) {
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", contractId);
//        req.putOnce("account", accountId);
//        SSQResult post = connector.post(BestSignPathEnum.CONTRACT_PREVIEW_URL.getPath(), JSONUtil.toJsonStr(req));
//        return JSONUtil.parseObj(post.getData()).getStr("url");
//    }
//
//    @Override
//    public String skipToSign(SsqSkipSignParam skipSignParam) {
//        JSONObject req = new JSONObject();
//        req.put("contractId", skipSignParam.getContractId());
//        req.put("tid", skipSignParam.getTid());
//        req.put("account", skipSignParam.getAccount());
//        req.put("returnUrl", skipSignParam.getReturnUrl());
//        req.put("isDrawSignatureImage", "1");
//        req.put("signatureImageName", "default");
//        req.put("signer", skipSignParam.getSigner());
//        req.put("varNames", skipSignParam.getVarNames());
//        SSQResult post = connector.post(BestSignPathEnum.CONTRACT_SEND_BY_TEMPLATE.getPath(), JSONUtil.toJsonStr(req));
//        String longUrl = JSONUtil.parseObj(post.getData()).getStr("longUrl");
//        return longUrl;
//    }
//
    @Override
    public void contractLock(String contractIds) {
        List<String> contractIdList = Func.toStrList(contractIds);
        for (String contractId : contractIdList) {
            JSONObject req = new JSONObject();
            req.putOnce("contractId", contractId);
            SSQResult post = connector.post(BestSignPathEnum.CONTRACT_LOCK.getPath(), JSONUtil.toJsonStr(req), false);
            if (CONTRACT_SIGNED.equals(post.getErrno()) || post.getErrno() == 0) {
                return;
            } else {
                log.error(post.getErrmsg());
                throw new ServiceException("合同锁定失败,请确保合同未过期并且处于有效状态");
            }
        }
    }
//
    @Override
    public SsqSignaturePositions getSignaturePositions(String fileBase64, String signAlign, String keywords) {
        //查找关键字坐标
        JSONObject req = new JSONObject();
        req.putOnce("keyword", keywords);
        req.putOnce("pdfData", fileBase64);
        SSQResult ssqResult = connector.post(BestSignPathEnum.PDF_FIND_KEYWORD_POSITIONS.getPath(), JSONUtil.toJsonStr(req));
        List<SSqKeyWordsPosition> positions1 = JSONUtil.toList(JSONUtil.parseObj(ssqResult.getData()).getStr("positions"), SSqKeyWordsPosition.class);
        if (CollectionUtil.isEmpty(positions1)) {
            throw new ServiceException("签署关键字配置错误");
        }
        SSqKeyWordsPosition keyWordsPositions = positions1.get(0);
        //获取具体签署位置
        SsqSignaturePositions positions = buildPosition(keyWordsPositions, signAlign);
        return positions;
    }
//
    @Override
    public String createContractByReport(ContractTemplateFileDTO contractTemplateFile) {
        //构建参数
        ContractCreateParam req = new ContractCreateParam();
        BeanUtil.copyProperties(contractTemplateFile, req);
        //TODO通过配置文件获取
//        BestSignConfig bestSignConfig = getBestSignConfig();
        req.setAccount(bestSignConfig.getAccount());
        SSQResult ssqResult = connector.post(BestSignPathEnum.CONTRACT_UPLOAD.getPath(), JSONUtil.toJsonStr(req));
        String contractId = JSONUtil.parseObj(ssqResult.getData()).getStr("contractId");
        return contractId;
    }

    @Override
    public SsqContractInfo getContractInfo(String contractId) {
        JSONObject contractReq = new JSONObject();
        contractReq.putOnce("contractId", contractId);
        SSQResult ssqResult = connector.post(BestSignPathEnum.CONTRACT_GET_INFO.getPath(), JSONUtil.toJsonStr(contractReq));
        SsqContractInfo ssqContractInfo = JSONUtil.toBean(ssqResult.getData(), SsqContractInfo.class);
        ssqContractInfo.setExpireTime(this.timeStamp2Date(ssqContractInfo.getExpireTime(), "YYYY-MM-dd HH:mm:ss"));
        return ssqContractInfo;
    }

    private String timeStamp2Date(String milliSecond, String format) {
        if (milliSecond != null && !milliSecond.isEmpty() && !milliSecond.equals("null")) {
            if (format == null || format.isEmpty()) {
                format = "yyyy-MM-dd HH:mm:ss";
            }

            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(new Date(Long.valueOf(milliSecond)));
        } else {
            return "";
        }
    }
//
//    @Override
//    public void contractFileSign() {
////        IContractOperatorService operatorService = SpringUtil.getBean(IContractOperatorService.class);
////        //获取当前签署人顺序
////        String contractId = contract.getContractId();
////        Integer signerSort = operatorService.currentCountByContractId(contractId, userId);
////        //根据签署人顺序 获取关键字配置选项
////        IContractSignConfigService signConfigService = SpringUtil.getBean(IContractSignConfigService.class);
////        List<ContractSignConfig> signConfigs = signConfigService.listByTemplateIdAndSort(contract.getContractTemplateId(), signerSort);
////        if (CollectionUtil.isEmpty(signConfigs)) {
////            throw new ServiceException("该合同不可继续签署或签署人员配置错误");
////        }
////        //获取签署关键字列表参数
////        Map<String, SsqSignaturePositions> positionsMap = calculateSignPosition(signConfigs, fileBase64);
////        //签署合同
////        JSONObject req = new JSONObject();
////        req.putOnce("contractId", contract.getContractId());
////        req.putOnce("signerAccount", MyAuthUtil.getCompanyId());
////        req.putOnce("signaturePositions", positionsMap.values());
////        connector.post(BestSignPathEnum.CONTRACT_SIGN_CERT.getPath(), JSONUtil.toJsonStr(req));
////        //添加当前签署者为签署人并更新状态为已签署
////        ContractOperator operator = new ContractOperator();
////        operator.setContractId(contractId);
////        operator.setSignerId(userId);
////        Integer label = MyAuthUtil.getLabel();
////        operator.setUserType(ObjectUtil.isEmpty(label) ? CustomerTypeEnum.PERSONAL.getCode() : label);
////        operator.setSignerName(MyAuthUtil.getCompanyName());
////        operator.setStatus(ContractEnum.CONTRACT_OPERATOR_STATUS.SIGN_COMPLETE.getStatus());
////        operatorService.save(operator);
////        //若签署人均签署完毕 锁定合同 否则更改签署状态为签署中
////        ContractTemplate contractTemplate = SpringUtil.getBean(IContractTemplateService.class).getById(contract.getContractTemplateId());
////        IContractService contractService = SpringUtil.getBean(IContractService.class);
////        if (signerSort.equals(contractTemplate.getSignerNum())) {
////            contractLock(contract.getContractId());
//////			contractService.updateContractStatus(contractId, ContractEnum.SSQ_CONTRACT_STATUS.COMPLETE.getStatus());
////        } else {
//////			contractService.updateContractStatus(contractId, ContractEnum.SSQ_CONTRACT_STATUS.SINGED.getStatus());
////        }
//    }
//
//    @Override
//    public void customizeAutoSign(SsqCustomerAutoSignParamNew paramNew) {
//        String signKeywordJsonStr = paramNew.getSignKeywordJson();
//        //获取关键字配置选项
//        List<SSqContractSignConfig> signConfigs = paramNew.getSignConfigs();
//        if (CollectionUtil.isEmpty(signConfigs)) {
//            throw new ServiceException("缺失关键字配置信息");
//        }
//        //获取签署关键字信息
//        JSONObject signKeywordJson = JSONUtil.parseObj(signKeywordJsonStr);
//        //根据用户类型进行签署
//        Integer userType = paramNew.getCustomerType();
//        if (CustomerTypeEnum.PERSONAL.getCode().equals(userType)) {
//            personCustomizeAutoSign(paramNew, signKeywordJson);
//        } else {
//            companyCustomizeAutoSign(paramNew, signKeywordJson);
//        }
//    }
//
//    @Override
//    public void templateAutoSign(SsqCustomerAutoSignParamNew signParam) {
//        Integer userType = signParam.getCustomerType();
//        //企业自动签署
//        Integer sealType = CustomerTypeEnum.PERSONAL.getCode().equals(userType) ? ContractConfigEnum.CATEGORY.SIGN.getCode() : ContractConfigEnum.CATEGORY.SEAL.getCode();
//        if (CollectionUtil.isEmpty(signParam.getSignConfigs())) {
//            throw new ServiceException("未配置签署关键字配置，请联系管理员");
//        }
//        Map<Integer, SSqContractSignConfig> signConfigMap = signParam.getSignConfigs().stream().collect(Collectors.toMap(SSqContractSignConfig::getSignType, e -> e, (oldVal, newVal) -> oldVal));
//        JSONObject signVars = new JSONObject();
//        SSqContractSignConfig stampConfig = signConfigMap.get(ContractEnum.signType.STAMP.getType());
//        SSqContractSignConfig timeConfig = signConfigMap.get(ContractEnum.signType.TIME.getType());
//        SSqContractSignConfig signConfig = signConfigMap.get(ContractEnum.signType.SIGN.getType());
//        //企业签署变量
//        if (CustomerTypeEnum.ENTERPRISE.getCode().equals(userType)) {
//            //若个人签署字段不为空 并且个人账号存在使用个人账户进行个人签署
//            String personId = signParam.getSignerPersonalId();
//            if (ObjectUtil.isNotEmpty(signConfig) && ObjectUtil.isNotEmpty(personId)) {
//                JSONObject companyUsePersonSignVar = new JSONObject();
//                companyUsePersonSignVar.putOnce(signConfig.getKeywords(), varsChange(signParam.getSignerPersonalSignatureImageName(), signParam.getCustomizeWriteBase64(), personId.toString()));
//                signParam.setSignerId(personId);
//                contractAutoSign(signParam.getContractId(), signParam.getTemplateId(), companyUsePersonSignVar);
//            }
//            signVars.putOnce(stampConfig.getKeywords(), varsChange(signParam.getSignatureImageName(), "", signParam.getSignerId()));
//        } else {
//            signVars.putOnce(signConfig.getKeywords(), varsChange(signParam.getSignatureImageName(), signParam.getCustomizeWriteBase64(), signParam.getSignerId()));
//        }
//        if (ObjectUtil.isNotEmpty(timeConfig)) {
//            signVars.putOnce(timeConfig.getKeywords(), varsChange("", "", signParam.getSignerId()));
//        }
//        signParam.setSignerId(signParam.getSignerId());
//        contractAutoSign(signParam.getContractId(), signParam.getTemplateId(), signVars);
//    }
//
//    private JSONObject varsChange(String signOrStampName, String signatureImageData, String account) {
//        JSONObject signOrStampVar = new JSONObject();
//        signOrStampVar.putOnce("account", account);
//        if (StringUtil.isNotBlank(signatureImageData)) {
//            signOrStampVar.putOnce("signatureImageData", signatureImageData);
//        } else if (StringUtil.isNotBlank(signOrStampName)) {
//            signOrStampVar.putOnce("signatureImageName", signOrStampName);
//        }
//        return signOrStampVar;
//    }
//
//    private void companyCustomizeAutoSign(SsqCustomerAutoSignParamNew signParam, JSONObject signKeywordJson) {
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", signParam.getContractId());
//        req.putOnce("signerAccount", signParam.getSignerId());
//        req.putOnce("signatureImageData", signParam.getSignatureImageName());
//        //构造签署位置参数
//        JSONArray array = new JSONArray();
//        for (SSqContractSignConfig signConfig : signParam.getSignConfigs()) {
//            Integer signType = signConfig.getSignType();
//            String keywords = signConfig.getKeywords();
//            if (signKeywordJson.containsKey(keywords)) {
//                JSONObject signPosition = signKeywordJson.getJSONObject(keywords);
//                //若企业需要签名 则使用其个人账户进行签名
//                if (ContractEnum.signType.SIGN.getType().equals(signType)) {
//                    customizeCompanyUsePersonAccountToSign(signParam, signPosition);
//                    continue;
//                }
//                //签署日期需添加标识符
//                if (ContractEnum.signType.TIME.getType().equals(signType)) {
//                    signPosition.putOnce("type", "date");
//                    signPosition.putOnce("dateTimeFormat", LocalDateTimeUtil.format(LocalDate.now(), "MM-dd-yyyy"));
//                }
//                array.add(signPosition);
//            }
//        }
//        req.putOnce("signaturePositions", array);
//        connector.post(BestSignPathEnum.CONTRACT_SIGN_CERT.getPath(), JSONUtil.toJsonStr(req));
//    }
//
//    private void customizeCompanyUsePersonAccountToSign(SsqCustomerAutoSignParamNew signParam, JSONObject signPosition) {
//        String customizeWriteBase64 = signParam.getCustomizeWriteBase64();
//        String contractId = signParam.getContractId();
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", contractId);
//        req.putOnce("signerAccount", signParam.getSignerPersonalId());
//        //使用手写板或是设置的签名图片进行签名
//        if (StringUtil.isNotBlank(customizeWriteBase64)) {
//            req.putOnce("signatureImageData", customizeWriteBase64);
//        } else {
//            req.putOnce("signatureImageName", signParam.getSignerPersonalSignatureImageName());
//        }
//        JSONArray array = new JSONArray();
//        array.add(signPosition);
//        req.putOnce("signaturePositions", array);
//        connector.post(BestSignPathEnum.CONTRACT_SIGN_CERT.getPath(), JSONUtil.toJsonStr(req));
//    }
//
//    /**
//     * 自定义模板进行个人自定义模板签名
//     *
//     * @param ssqAutoSignParamNew
//     */
//    private void personCustomizeAutoSign(SsqCustomerAutoSignParamNew ssqAutoSignParamNew, JSONObject signKeywordJson) {
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", ssqAutoSignParamNew.getContractId());
//        req.putOnce("signerAccount", ssqAutoSignParamNew.getSignerId());
//        //若用户传入手写板签名 使用手写板签名
//        String customizeWriteBase64 = ssqAutoSignParamNew.getCustomizeWriteBase64();
//        if (StringUtil.isNotBlank(customizeWriteBase64)) {
//            req.putOnce("signatureImageData", customizeWriteBase64);
//        } else {
//            req.putOnce("signatureImageName", ssqAutoSignParamNew.getSignatureImageName());
//        }
//        //构造签署位置参数
//        JSONArray array = new JSONArray();
//        for (SSqContractSignConfig signConfig : ssqAutoSignParamNew.getSignConfigs()) {
//            Integer signType = signConfig.getSignType();
//            String keywords = signConfig.getKeywords();
//            if (signKeywordJson.containsKey(keywords) && !ContractEnum.signType.STAMP.getType().equals(signType)) {
//                JSONObject signPosition = signKeywordJson.getJSONObject(keywords);
//                //签署日期需添加标识符
//                if (ContractEnum.signType.TIME.getType().equals(signType)) {
//                    signPosition.putOnce("type", "date");
//                    signPosition.putOnce("dateTimeFormat", LocalDateTimeUtil.format(LocalDate.now(), "MM-dd-yyyy"));
//                }
//                array.add(signPosition);
//            }
//        }
//        req.putOnce("signaturePositions", array);
//        connector.post(BestSignPathEnum.CONTRACT_SIGN_CERT.getPath(), JSONUtil.toJsonStr(req));
//    }
//
    /**
     * @param keyWordsPositions 关键字位置信息
     * @param signAlign         关键字位置
     */
    private SsqSignaturePositions buildPosition(SSqKeyWordsPosition keyWordsPositions, String signAlign) {
        SsqSignaturePositions positions = new SsqSignaturePositions();
        //1 左上角 2 右上角 3 左下角 4 右下角
        String x;
        String y;
        switch (signAlign) {
            case "1":
                x = keyWordsPositions.getX1();
                y = keyWordsPositions.getY1();
                break;
            case "2":
                x = keyWordsPositions.getX2();
                y = keyWordsPositions.getY1();
                break;
            case "3":
                x = keyWordsPositions.getX1();
                y = keyWordsPositions.getY2();
                break;
            case "4":
                x = keyWordsPositions.getX2();
                y = keyWordsPositions.getY2();
                break;
            default:
                throw new ServiceException("关键字坐标配置错误，请联系管理员");
        }
        positions.setX(x);
        positions.setY(y);
        positions.setPageNum(keyWordsPositions.getPageNum());
        return positions;
    }
//
//    /**
//     * 过滤出group相同的 取其中一个即可 并将其作为新的name
//     *
//     * @param templateVars
//     * @return
//     */
//    private JSONArray filterFieldsByGroup(List<SsqTemplateVars> templateVars) {
//        //建立filter过滤器，若存在则不添加 效果 ：多条记录相同group情况下 只保留一条 并将名称改为group以便后续生成合同时的赋值操作
//        Map<String, SsqTemplateVars> filter = new HashMap<>();
//        //建立新的字段待选区
//        JSONArray newFields = new JSONArray();
//        templateVars.forEach(e -> {
//            String group = e.getGroup();
//            if (ObjectUtil.isNotEmpty(group)) {
//                if (!filter.containsKey(group)) {
//                    e.setName(e.getGroup());
//                    newFields.add(e);
//                }
//                filter.put(group, e);
//            } else {
//                newFields.add(e);
//            }
//        });
//        return newFields;
//    }
//
//    /**
//     * 模板跳转签名
//     *
//     * @return
//     */
//    private String templateSkipSign(SsqSkipSignParam skipSignParam) {
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", skipSignParam.getContractId());
//        req.putOnce("tid", skipSignParam.getTid());
//        req.putOnce("signer", skipSignParam.getSigner());
//        req.putOnce("isDrawSignatureImage", "1");
//        req.putOnce("signatureImageName", "");
//        req.putOnce("pushUrl", "");
//        req.putOnce("returnUrl", skipSignParam.getReturnUrl());
//        req.putOnce("isAllowChangeSignaturePosition", "1");
//        //根据用户类型 填充签署变量
//        StringBuilder varNames = new StringBuilder();
//        //为企业 还需要进行自动签署姓名
//        StringBuilder personVar = new StringBuilder();
//        req.putOnce("varNames", varNames.toString());
//        String personStr = personVar.toString();
//        //若企业需要进行签名。则使用其个人账号进行自动签名
//        if (!StringUtil.isEmpty(personStr)) {
//            //自动签署签名
//            contractUsePersonAutoSign(skipSignParam.getContractId(), skipSignParam.getTid());
//        }
//        SSQResult ssqResult = connector.post(BestSignPathEnum.CONTRACT_SEND_BY_TEMPLATE.getPath(), JSONUtil.toJsonStr(req));
//        //签署链接
//        String longUrl = JSONUtil.parseObj(ssqResult.getData()).getStr("longUrl");
//        return longUrl;
//    }
//
//    /**
//     * 使用个人进行自动签署
//     *
//     * @param contractId
//     * @param templateId
//     */
//    private void contractUsePersonAutoSign(String contractId, String templateId) {
//        contractAutoSign(contractId, templateId, null);
//    }
//
//    /**
//     * 获取签署链接（即手动签）
//     *
//     * @return
//     */
//    private String contractSend(String accountId) {
//        JSONObject req = new JSONObject();
//        req.putOnce("contractId", accountId);
//        req.putOnce("faceMethod", "");
//        req.putOnce("signer", accountId);
//        //设置签署关键字坐标
//        JSONObject signaturePositions = new JSONObject();
//            throw new UnsupportedOperationException("TODO");
//    }
//
//    private BladeFile downLoadContractById(String contractId, String contractTitle) {
//        JSONObject contractReq = new JSONObject();
//        contractReq.putOnce("contractId", contractId);
//        return connector.getToFile(BestSignPathEnum.CONTRACT_DOWNLOAD.getPath(), contractReq, contractTitle + IdWorker.getIdStr() + ".pdf");
//    }
//
//    /**
//     * 上上签合同token生成及合同创建
//     *
//     * @param template 合同模板信息
//     * @return
//     */
//    private String ssqCreateContract(SsqContractGenParam template, String useTemplateReq) {
//        BestSignConfig params = getBestSignConfig();
//        String templateId = template.getTemplateId();
//        String devAccount = params.getAccount();
//        //通过模版生成合同文件获取templateToken
//        //生成合同token
//        SSQResult templateCreateResult = connector.post(BestSignPathEnum.TEMPLATE_CREATE_CONTRACT_PDF.getPath(), JSONUtil.toJsonStr(useTemplateReq));
//        String templateToken = JSONUtil.parseObj(templateCreateResult.getData()).getStr("templateToken");
//        //使用templateToken正式生成合同
//        JSONObject contractGenReq = new JSONObject();
//        contractGenReq.putOnce("account", devAccount);
//        contractGenReq.putOnce("tid", templateId);
//        contractGenReq.putOnce("templateToken", templateToken);
//        String expireTimeUnix = "";
//        if (ObjectUtil.isNotEmpty(template.getExpireDay())) {
//            Long expireDay = (System.currentTimeMillis() / 1000) + template.getExpireDay().longValue() * 86400;
//            expireTimeUnix = expireDay.toString();
//        }
//        contractGenReq.putOnce("expireTime", expireTimeUnix);
//        contractGenReq.putOnce("title", template.getTemplateName());
//        contractGenReq.putOnce("description", "");
//        SSQResult contractGenResult = connector.post(BestSignPathEnum.CONTRACT_CREATE_BY_TEMPLATE.getPath(), JSONUtil.toJsonStr(contractGenReq));
//        String contractId = JSONUtil.parseObj(contractGenResult.getData()).getStr("contractId");
//        return contractId;
//    }
//
//    /**
//     * 生成合同token 串
//     *
//     * @param templateId
//     * @param groupValues
//     * @param templateValues
//     * @return
//     */
//    private String genTokenReq(String templateId, Map<String, Object> groupValues, Map<String, Object> templateValues) {
//        JSONObject useTemplateReq = new JSONObject();
//        //使用开发者账号进行发送
//        BestSignConfig params = getBestSignConfig();
//        String devAccount = params.getAccount();
//        useTemplateReq.putOnce("account", devAccount);
//        useTemplateReq.putOnce("groupValues", CollectionUtil.isEmpty(groupValues) ? "" : groupValues);
//        useTemplateReq.putOnce("tid", templateId);
//        useTemplateReq.putOnce("templateValues", CollectionUtil.isEmpty(templateValues) ? "" : templateValues);
//        return JSONUtil.toJsonStr(useTemplateReq);
//    }
//
    private static BestSignConfig getBestSignConfig() {
        BestSignConfig params = OtherApiUtils.getParams(OtherApiTypeEnum.ELEC_SIGN.getCode(), BestSignConfig.class);
        return params;
    }
//
//    @Override
//    public SsqUserBaseInfo userBaseInfo(String account) {
//        JSONObject req = new JSONObject();
//        req.putOnce("account", account);
//        SSQResult post = connector.post(BestSignPathEnum.USER_BASE_INFO.getPath(), JSONUtil.toJsonStr(req));
//        return JSONUtil.toBean(post.getData(), SsqUserBaseInfo.class);
//    }
//
//    @Override
//    public SsqCertInfo checkCerInfo(String account) {
//        JSONObject req = new JSONObject();
//        req.putOnce("account", account);
//        SSQResult result = null;
//        try {
//            result = connector.post(BestSignPathEnum.USER_GET_CERT.getPath(), JSONUtil.toJsonStr(req));
//        } catch (ServiceException e) {
//            throw new ServiceException("账号不存在,请联系管理员");
//        }
//        //查询证书
//        String certId = JSONUtil.parseObj(result.getData()).getStr("certId");
//        JSONObject req2 = new JSONObject();
//        req2.putOnce("certId", certId);
//        req2.putOnce("account", account);
//        SSQResult cerResult = connector.post(BestSignPathEnum.USER_CERT_INFO.getPath(), JSONUtil.toJsonStr(req2));
//        return JSONUtil.toBean(cerResult.getData(), SsqCertInfo.class);
//    }
//
//    @Override
//    public void reApplyCert(String account) {
//        JSONObject req3 = new JSONObject();
//        req3.putOnce("account", account);
//        connector.post(BestSignPathEnum.USER_REAPPLY_CERT.getPath(), JSONUtil.toJsonStr(req3));
//    }
//
//    @Override
//    public ContractReturnData contractResend(SsqContractGenParam template, String oldContractVar) {
//        if (StringUtil.isEmpty(oldContractVar)) {
//            throw new ServiceException("合同变量缺失");
//        }
//        //合同生成
//        String contractId = ssqCreateContract(template, oldContractVar);
//        //合同下载
//        String link = downLoadContractById(contractId, IdWorker.getIdStr()).getLink();
//        ContractReturnData contractReturnData = new ContractReturnData(link, contractId);
//        contractReturnData.setContractVar(oldContractVar);
//        return contractReturnData;
//    }
//
    @Override
    public boolean delayExpireTime(List<String> contractIds, String expireTime) {
        long timeMillis = System.currentTimeMillis() / 1000;
        if (timeMillis > Long.parseLong(expireTime)) {
            throw new ServiceException("不可小于当前时间");
        }
        JSONObject re = new JSONObject();
        re.putOnce("contractIds", contractIds);
        re.putOnce("expireTime", expireTime);
//        connector.post(BestSignPathEnum.DELAY_EXPIRE_TIME.getPath(), JSONUtil.toJsonStr(re));
        return true;
    }
//
//    @Override
//    public String regPersonUser(SSqPersonAuth personAuth) {
//        SSQResult ssqResult = connector.post(BestSignPathEnum.USER_REG.getPath(), JSONUtil.toJsonStr(personAuth), true);
//        return JSONUtil.parseObj(ssqResult.getData()).getStr("taskId");
//    }
//
//    @Override
//    public SSQResult commitPersonAuthInfoOnPhone(SSqPersonAuthPhoneParam phoneParam) {
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY3_VCODE_SENDER.getPath(), JSONUtil.toJsonStr(phoneParam), true);
//    }
//
//    @Override
//    public SSQResult commitPersonAuthInfoOnFace(SSqPersonAuthFaceParam faceParam) {
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY2_ALIPAYFACE.getPath(), JSONUtil.toJsonStr(faceParam), true);
//    }
//
//    @Override
//    public SSQResult commitPersonAuthInfoOnBank(SSqPersonAuthBankParam bankParam) {
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY4_VCODE_SENDER.getPath(), JSONUtil.toJsonStr(bankParam), true);
//    }
//
//    @Override
//    public SSQResult commitPersonAuthInfoOnWxAppletFace(SSqPersonAuthFaceParam faceParam) {
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY2_WXAPPLETFACE.getPath(), JSONUtil.toJsonStr(faceParam), true);
//    }
//
//    @Override
//    public SSQResult commitPersonAuthInfoOnFaceContrast(SSqPersonAuthFaceContrastParam faceContrastParam) {
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY3_FACE_COMPARISON.getPath(), JSONUtil.toJsonStr(faceContrastParam), true);
//    }
//
//    @Override
//    public SSQResult commitPersonAuthInfoOnH5FaceContrast(SSqPersonAuthFaceH5Param param) {
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY2_H5_COMPARISON.getPath(), JSONUtil.toJsonStr(param), false);
//    }
//
//    @Override
//    public SSQResult verifyPersonAuthInfoOnPhone(String identity3Key, String vcode) {
//        JSONObject req = new JSONObject();
//        req.putOnce("personalIdentity3Key", identity3Key);
//        req.putOnce("vcode", vcode);
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY3_VCODE_VERIFY.getPath(), JSONUtil.toJsonStr(req), true);
//    }
//
//    @Override
//    public SSQResult verifyPersonAuthInfoOnBank(String identity3Key, String vcode) {
//        JSONObject req = new JSONObject();
//        req.putOnce("identity3Key", identity3Key);
//        req.putOnce("vcode", vcode);
//        return connector.post(BestSignPathEnum.PERSON_IDENTITY4_VCODE_VERIFY.getPath(), JSONUtil.toJsonStr(req), true);
//    }
//
//    @Override
//    public String regEntUser(SSqEntReg sSqEntReg) {
//        SSQResult ssqResult = connector.post(BestSignPathEnum.USER_REG.getPath(), JSONUtil.toJsonStr(sSqEntReg), true);
//        return JSONUtil.parseObj(ssqResult.getData()).getStr("taskId");
//    }
//
    @Override
    public SSQResult entPayAuthQuery(String bizNo) {
        JSONObject req = new JSONObject();
        req.putOnce("bizNo", bizNo);
        return connector.post(BestSignPathEnum.ENT_PAY_AUTH_QUERY.getPath(), JSONUtil.toJsonStr(req), true);
    }

    @Override
    public SSQResult entCorporateAccountAudit(SSQAccountAudit accountAudit) {
        return connector.post(BestSignPathEnum.ENT_CORPORATE_ACCOUNT_AUDIT.getPath(), JSONUtil.toJsonStr(accountAudit), true);
    }

    @Override
    public SSQResult payAuthVerify(String bankCard, String transactionAmount) {
        JSONObject req = new JSONObject();
        req.putOnce("bankCard", bankCard);
        req.putOnce("transactionAmount", transactionAmount);
        return connector.post(BestSignPathEnum.ENT_PAYAUTH_VERIFY.getPath(), JSONUtil.toJsonStr(req));
    }
//
//    @Override
//    public PersonEAuthInfoDTO getPersonalCredential(String account) {
//        JSONObject req = new JSONObject();
//        req.putOnce("account", account);
//        SSQResult ssqResult = connector.post(BestSignPathEnum.GET_PERSONAL_CREDENTIAL.getPath(), JSONUtil.toJsonStr(req), false);
//        //账号不存在 返回null
//        if (!BestSignConnector.isSuccess(ssqResult)) {
//            return null;
//        }
//        return JSONUtil.toBean(ssqResult.getData(), PersonEAuthInfoDTO.class);
//    }
//
//    @Override
//    public EntEAuthInfoDTO getEntCredential(String account) {
//        JSONObject req = new JSONObject();
//        req.putOnce("account", account);
//        SSQResult ssqResult = connector.post(BestSignPathEnum.GET_ENT_CREDENTIAL.getPath(), JSONUtil.toJsonStr(req), false);
//        //账号不存在 返回null
//        if (!BestSignConnector.isSuccess(ssqResult)) {
//            return null;
//        }
//        return JSONUtil.toBean(ssqResult.getData(), EntEAuthInfoDTO.class);
//    }
}
