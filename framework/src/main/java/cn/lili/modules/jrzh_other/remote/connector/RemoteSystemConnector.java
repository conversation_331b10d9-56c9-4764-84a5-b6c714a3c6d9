package cn.lili.modules.jrzh_other.remote.connector;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.aop.annotation.RetryOperation;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.Base64Utils;
import cn.lili.common.utils.SignUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_other.remote.config.RemoteApiConfig;
import cn.lili.modules.jrzh_other.remote.constant.RemoteSystemConstant;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 3.0系统接口连接器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RemoteSystemConnector {

    private final RemoteApiConfig remoteApiConfig;

    // 超时配置常量
    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;
    // 重试间隔秒数
    private static final int RETRY_DELAY_SECONDS = 2;

    /**
     * 发送POST请求到远程系统
     *
     * @param url         请求路径
     * @param requestData 请求数据
     * @param needDecrypt 是否解密
     * @return 响应结果
     */
    @RetryOperation(retryCount = MAX_RETRY_COUNT, waitSeconds = RETRY_DELAY_SECONDS)
    public ResultMessage post(String url, String requestData, Boolean needDecrypt) {
        String requestId = generateRequestId();

        try {
            // 1. 参数验证
            validateParameters(url, requestData);

            // 2. 构建请求URL
            String requestUrl = buildRequestUrl(url);

            log.info("开始远程调用 - RequestId: {}, URL: {}, 请求数据: {}", requestId, requestUrl, requestData);

            // 3. 加密请求数据
            String encryptedBody = encryptRequestData(requestData, requestId);

            // 4. 发送HTTP请求
            String responseBody = sendHttpRequest(requestUrl, encryptedBody, requestId);

            // 5. 解析响应
            ResultMessage result = parseResponse(responseBody, requestId, needDecrypt);

            // 6. 处理错误（如果需要）
//            if (dealError != null && dealError) {
//                dealErrorResult(result);
//            }
            log.info("远程调用成功 - RequestId: {}, 响应: {}", requestId, JSONUtil.toJsonStr(result));
            return result;

        } catch (Exception e) {
            log.error("远程调用系统异常 - RequestId: {}, 错误: {}", requestId, e.getMessage(), e);
            throw new ServiceException("系统异常");
        }
    }

    /**
     * 构建请求URL
     */
    private String buildRequestUrl(String url) {
        String host = remoteApiConfig.getHost();
        if (StringUtil.isBlank(host)) {
            throw new ServiceException("远程系统主机地址未配置");
        }
        if (StringUtil.isBlank(url)) {
            throw new ServiceException("请求路径不能为空");
        }
        return host +"/api"+ url;
    }

    /**
     * 生成请求ID用于日志追踪
     */
    private String generateRequestId() {
        return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }

    /**
     * 验证请求参数
     */
    private void validateParameters(String url, String requestData) {
        if (StringUtil.isBlank(url)) {
            throw new ServiceException("请求URL不能为空");
        }
        if (StringUtil.isBlank(requestData)) {
            throw new ServiceException("请求数据不能为空");
        }
    }

    /**
     * 加密请求数据
     */
    private String encryptRequestData(String requestData, String requestId) {
        try {
            String encryptedBody = SignUtil.requestEncrypt(requestData, RemoteSystemConstant.SUPPLY_CHAIN_FINANCE_3);
            log.debug("请求数据加密成功 - RequestId: {}, 原始数据: {}", requestId, requestData);

            // 验证加密结果
            if (StringUtil.isBlank(encryptedBody)) {
                throw new ServiceException("请求数据加密失败");
            }

            return encryptedBody;
        } catch (Exception e) {
            log.error("请求数据加密异常 - RequestId: {}, 错误: {}", requestId, e.getMessage(), e);
            throw new ServiceException("请求数据加密失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTTP请求
     */
    private String sendHttpRequest(String requestUrl, String encryptedBody, String requestId) {
        HttpResponse response = null;
        try {
            String authorization = remoteApiConfig.getAuthorization();
            if (StringUtil.isBlank(authorization)) {
                throw new ServiceException("远程系统认证信息未配置");
            }

            log.debug("发送HTTP请求 - RequestId: {}, URL: {}", requestId, requestUrl);

            HttpRequest request = HttpUtil.createPost(requestUrl)
                    .header("Authorization", "Basic " + Base64Utils.encode(authorization))
//                    .header("Content-Type", "application/json")
//                    .header("User-Agent", "RemoteSystemConnector/1.0")
//                    .timeout(CONNECTION_TIMEOUT)
//                    .setReadTimeout(READ_TIMEOUT)
                    .body(encryptedBody);

            response = request.execute();

            // 检查HTTP状态码
            int statusCode = response.getStatus();
            if (statusCode != 200) {
                log.error("HTTP请求失败 - RequestId: {}, 状态码: {}, 响应: {}",
                        requestId, statusCode, response.body());
                throw new ServiceException("远程系统HTTP请求失败，状态码: " + statusCode);
            }

            String responseBody = response.body();
            if (StringUtil.isBlank(responseBody)) {
                throw new ServiceException("远程系统返回空响应");
            }

            log.debug("HTTP请求成功 - RequestId: {}, 响应长度: {}", requestId, responseBody.length());
            return responseBody;

        } catch (Exception e) {
            log.error("HTTP请求异常 - RequestId: {}, 错误: {}", requestId, e.getMessage(), e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("HTTP请求失败: " + e.getMessage());
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                    log.warn("关闭HTTP响应异常 - RequestId: {}, 错误: {}", requestId, e.getMessage());
                }
            }
        }
    }

    /**
     * 解析响应数据
     */
    private ResultMessage parseResponse(String responseBody, String requestId, Boolean needDecrypt) {
        try {
            log.debug("开始解析响应 - RequestId: {}, 响应: {}", requestId, responseBody);

            JSONObject jsonObject = JSONUtil.parseObj(responseBody);
            ResultMessage result = JSONUtil.toBean(responseBody, ResultMessage.class, true);

            // 设置消息字段（兼容msg和message字段）
            String message = jsonObject.getStr("msg");
            if (StringUtil.isBlank(message)) {
                message = jsonObject.getStr("message");
            }
            result.setMessage(message);

            // 处理数据字段
            Boolean success = jsonObject.getBool("success");
            if (success != null && success) {
                // 成功响应，解密数据
                String data = jsonObject.getStr("data");
                if (StringUtil.isNotBlank(data)&&needDecrypt) {
                    try {
                        String decryptedData = SignUtil.responseDecrypt(data, RemoteSystemConstant.SUPPLY_CHAIN_FINANCE_3);
                        result.setResult(decryptedData);
                        log.debug("响应数据解密成功 - RequestId: {}", requestId);
                    } catch (Exception e) {
                        log.error("响应数据解密失败 - RequestId: {}, 错误: {}", requestId, e.getMessage(), e);
                        throw new ServiceException("响应数据解密失败: " + e.getMessage());
                    }
                } else {
                    result.setResult(data);
                }
            } else {
                // 失败响应，直接设置原始数据
                result.setResult(jsonObject.getStr("data"));

                // 检查特定的错误消息并抛出相应的异常
                String errorMessage = result.getMessage();
                if (StringUtil.isNotBlank(errorMessage) && errorMessage.contains("账号已被注册,请重新输入!")) {
                    throw new ServiceException(errorMessage);
                }
            }

            log.debug("响应解析成功 - RequestId: {}, 成功: {}, 消息: {}",
                    requestId, result.isSuccess(), result.getMessage());
            return result;

        } catch (Exception e) {
            log.error("响应解析异常 - RequestId: {}, 错误: {}", requestId, e.getMessage(), e);
            if (e instanceof ServiceException) {
                throw e;
            }
            throw new ServiceException("响应解析失败: " + e.getMessage());
        }
    }

}
