package cn.lili.modules.jrzh_other.remote.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.entity.CustomerPersonInfo;
import cn.lili.modules.connect.service.ICustomerInfoService;
import cn.lili.modules.connect.service.ICustomerPersonInfoService;
import cn.lili.modules.jrzh_bases.IAttachService;
import cn.lili.modules.jrzh_contract.contract_api.entity.Attach;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignSeal;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractConfigService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractSignSealService;
import cn.lili.modules.jrzh_other.remote.connector.RemoteSystemConnector;
import cn.lili.modules.jrzh_other.remote.constant.RemotePathEnum;
import cn.lili.modules.jrzh_other.remote.constant.RemoteSystemConstant;
import cn.lili.modules.jrzh_other.remote.dto.CustomerAccountConnectionDTO;
import cn.lili.modules.jrzh_other.remote.handler.account.impl.AbstractAccountHandler;
import cn.lili.modules.member.entity.dos.Member;
import cn.lili.modules.member.service.MemberService;
import cn.lili.modules.promotion.entity.dto.MemberDTO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供金3.0系统账户相关信息推送
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service(RemoteSystemConstant.SUPPLY_CHAIN_FINANCE_3 + RemoteSystemConstant.ACCOUNT_HANDLER)
public class ThreeAccountHandler extends AbstractAccountHandler<CustomerAccountConnectionDTO> {

    private final ICustomerInfoService customerInfoService;
    private final MemberService memberService;
    private final RemoteSystemConnector remoteSystemConnector;
    private final ICustomerPersonInfoService customerPersonInfoService;
    private final IContractSignSealService contractSignSealService;
    private final IContractConfigService contractConfigService;
    private final IAttachService attachService;

    @Override
    public CustomerAccountConnectionDTO prepareParams(MemberDTO memberDto) {
        String memberId = memberDto.getId();
        Member member = memberService.getById(memberId);
        if (ObjectUtil.isEmpty(member)) {
            throw new ServiceException("查询不到会员信息");
        }
        Long longMemberId = Long.valueOf(member.getId());

        CustomerInfo customerInfo = customerInfoService.getBuyCustomerId(member.getId());

        CustomerPersonInfo personInfo = customerPersonInfoService.getByCustomerId(longMemberId);

        List<ContractConfig> contractConfigs = contractConfigService.list(Wrappers.<ContractConfig>lambdaQuery().eq(ContractConfig::getUserId, longMemberId));

        List<Long> signSealIds = new ArrayList<>();
        if (1 == contractConfigs.size() && null != contractConfigs.get(0).getSignId() && null != contractConfigs.get(0).getSealId()) {
            signSealIds.add(contractConfigs.get(0).getSignId());
            signSealIds.add(contractConfigs.get(0).getSealId());
        } else {
            signSealIds = contractConfigs.stream().map(e -> ObjectUtil.isNull(e.getSignId()) ? e.getSealId() : e.getSignId()).collect(Collectors.toList());
        }
        List<ContractSignSeal> contractSignSeals = contractSignSealService.listByIds(signSealIds);

        List<Long> attachIds = new ArrayList<>();
        attachIds.add(Long.valueOf(customerInfo.getCorporationBackAttachId()));
        attachIds.add(Long.valueOf(customerInfo.getCorporationFaceAttachId()));
        attachIds.add(Long.valueOf(customerInfo.getBusinessLicenceAttachId()));
        attachIds.add(Long.valueOf(personInfo.getIdentityBackfileAttachid()));
        attachIds.add(Long.valueOf(personInfo.getIdentityBackfileAttachid()));
        List<Attach> attachList = attachService.listByIds(attachIds);

        CustomerAccountConnectionDTO dto = new CustomerAccountConnectionDTO();
        dto.setMemberId(member.getId());
        dto.setAccountUser(null == memberDto.getUserName() ? member.getUsername() : memberDto.getUserName());
        dto.setPassword(member.getPasswordFront());
        dto.setPhone(member.getMobile());
        dto.setCustomerInfo(customerInfo);
        dto.setCustomerPersonInfo(personInfo);
        dto.setContractConfigList(contractConfigs);
        dto.setContractSignSealList(contractSignSeals);
        dto.setAttachList(attachList);
        dto.setPersonSignSealAccount(member.getId() + "person");
        dto.setEntSignSealAccount(member.getId());
        return dto;
    }

    @Override
    public ResultMessage accountConnection(CustomerAccountConnectionDTO dto) {
        ResultMessage resultMessage = remoteSystemConnector.post(RemotePathEnum.ACCOUNT_CONNECTION.getPath(), JSONUtil.toJsonStr(dto), true);
        if (500 == (resultMessage.getCode())&&"账号已被注册,请重新输入!".equals(resultMessage.getMessage())) {
            throw new ServiceException("当前账号:"+dto.getAccountUser()+"在融资平台已被注册，请输入新的账号");
        }
        Map<String, Object> parseObj = JSONUtil.parseObj(resultMessage.getResult());
        parseObj.put("id", parseObj.get("id").toString());
        parseObj.put("typeId", parseObj.get("typeId").toString());
        resultMessage.setResult(parseObj);
        return resultMessage;
    }

    @Override
    public void success(CustomerAccountConnectionDTO dto, String memberId) {
        memberService.update(Wrappers.<Member>lambdaUpdate().eq(Member::getId, memberId).set(Member::getConnectSysType, 1));
    }

    /**
     * 查询产品和额度
     *
     * @return
     */
    @Override
    public ResultMessage getGoodsAndQuota(String memberId) {
        ResultMessage result = remoteSystemConnector.post(RemotePathEnum.GET_GOODS_AND_QUOTA.getPath(), memberId, true);
        if ("融资用户信息不存在".equals(result.getMessage())) {
            result.setMessage("0");
            result.setCode(ResultCode.SUCCESS.code());
            result.setSuccess(Boolean.TRUE);
            return result;
        } else if (null == result.getResult()) {
            result.setMessage("1");
            return result;
        }
        List<Map> list = JSONUtil.toList(result.getResult().toString(), Map.class);
        BigDecimal totalAvailable = BigDecimal.ZERO;
        for (Map map : list) {
            totalAvailable = totalAvailable.add(new BigDecimal(map.get("totalAvailable").toString()));
        }
        return ResultUtil.data(totalAvailable.toString());
    }


}
