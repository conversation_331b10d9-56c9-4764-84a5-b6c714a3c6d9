package cn.lili.modules.jrzh_other.remote.handler.account.impl;

import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_other.remote.handler.account.AccountHandler;
import cn.lili.modules.promotion.entity.dto.MemberDTO;

/**
 * 账户相关业务
 *
 * @param <T>
 * <AUTHOR>
 * @since 2025-6-17
 */
public abstract class AbstractAccountHandler<T> implements AccountHandler<T> {

    @Override
    public T prepareParams(MemberDTO memberDto) {
        return null;
    }

    @Override
    public void before(T t, String memberId) {

    }

    @Override
    public void after(T t, String memberId) {

    }

    @Override
    public ResultMessage accountConnection(T t) {
        return null;
    }

    @Override
    public ResultMessage getGoodsAndQuota(String memberId) {
        return null;
    }

    @Override
    public void success(T t, String memberId) {

    }

    @Override
    public void fail(T t, String memberId) {

    }

    @Override
    public ResultMessage handler(MemberDTO memberDto) {
        String memberId = memberDto.getId();
        //账号相关参数处理
        T t = prepareParams(memberDto);
        //操作前
        before(t, memberId);
        //处理中
        ResultMessage result = accountConnection(t);
        //判断
        if (null != result) {
            //执行成功处理逻辑
            success(t, memberId);
        } else {
            //执行失败处理逻辑
            fail(t, memberId);
        }
        //操作后
        after(t, memberId);
        return result;
    }

}
